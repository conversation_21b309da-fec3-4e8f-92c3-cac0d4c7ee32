<template>
  <div class="all-background product-content">
    <div class="template-content">
      <div @click="treeshow = !treeshow" class="hiddentreebtn" :style="{ left: !treeshow ? '0px' : '210px' }">
        <a-icon :type="!treeshow ? 'menu-unfold' : 'menu-fold'" />
      </div>
      <!-- 默认占位 -->
      <div class="template-title" v-show="treeshow"></div>
      <VueDragResize v-show="treeshow" :w="210" :minw="210" contentClass="template-title template-title-border" :sticks="['mr']" :isDraggable="false" style="z-index: 50;">
        <div class="template-title" >
        <div class="tree-title">
          <p class="title">{{ $t("txt_folder_structure") }}</p>
        </div>
        <a-tree ref="folderTree" class="trfoldertree" :selectedKeys="selectedKeys" :replaceFields="replaceFields" :expanded-keys="expandedKeys" :auto-expand-parent="autoExpandParent" :tree-data="contentTreeData" @expand="onExpand" @select="onSelect">
          <template slot="title" slot-scope="{ title, key, dataRef }">
            <div class="tree-self">
              <jw-icon :type="
              dataRef.childType === 'child'
                    ? '#jwi-wenjianga-youneiyong'
                    : '#jwi-chanpin'
                "/>
              <span class="tree-self-title" :title="title">
                {{ title }}
              </span>
              <div class="tree-self-opration-folder">
                <folder-btn :folder="dataRef" @create="showAddFolder(key, dataRef)" @rename="showRename(dataRef)" @delete="deleteFolder(key, dataRef)" @createByUse="createByUse(dataRef)">
                </folder-btn>
              </div>
            </div>
          </template>
        </a-tree>
        </div>
        </VueDragResize>
      <div class="template-list">
        <a-spin :spinning="cardSpinning">
          <jw-table ref="ref_table" disableCheck="disableCheck" :data-source.sync="tableData" :columns="getHeader" :selectedRows.sync="selectedRows" :toolbars="toolbars" :pagerConfig="pagerConfig" :checkbox-config="{reserve: true}" @onPageChange="onPageChange" @onSizeChange="onSizeChange" @onToolClick="onToolClick" @onToolInput="onToolInput"@checkbox-change="onSelectChange" @onOperateClick="onOperateClick" @on-checkbox-change="onCheckboxChange" @checkbox-all="onCheckboxChange">
            <template slot="tool-after-end">

              <a-tag class="table-tag" v-if="!switchType&&selectedRows.length" color="blue" closable @close="clearTableSelect">已选中{{ selectedRows.length }}项</a-tag>
              <a-button style="margin-left:8px;"
                  @click="
                  onToolClick({ name: '', position: 'after', key: 'switch' })
                "
              >
                <jw-icon
                    :type="
                    this.switchType === false
                      ? 'jwi-iconlogo-windows-filled'
                      : 'jwi-iconlist'
                  "
                >
                </jw-icon>
              </a-button>
              <batch-operator
                  ref="batch-operator"
                  @reloadpage="reFetchData"
                  :selectList.sync="selectedRows"
                  :containerOid="$route.query.oid"
                  :containerModel="$route.query.modelDefinition"
                  :containerType="$route.query.type"
                  :erp="true"
                  :isProduct="true"
                  :isAdmin=isAdmin
              />
              <a-dropdown placement="bottomRight">
                <a-menu slot="overlay">
                  <!-- <a-menu-item key="partKey" @click="visibleExport = true"
                    :disabled="!permissionList.includes('Part.create')"
                  >{{
                      $t("txt_import_part")
                    }}</a-menu-item> -->
                  <!-- part导入部件和结构有效性等 -->
                 <!-- <a-menu-item
                     key="partbomKey"
                     @click="visiblePartBomExport = true"
                 >{{ $t("txt_import_partbom") }}</a-menu-item
                 >
                 <a-menu-item
                     key="partDocumentKey"
                     @click="openImportModal('Part')"
                 >{{ $t("part_document_import") }}</a-menu-item
                 > -->
                  <!-- <a-menu-item
                      key="DocumentKey"
                      @click="batchImportVisible = true"
                      :disabled="!permissionList.includes('Document.create')"
                  >{{ $t("txt_import_document") }}</a-menu-item> -->
                  <!-- 批量导入模型文件 -->
                  <a-menu-item key="mcadKey" @click="openImportModal('MCAD')">{{
                      $t("txt_upload_model_file")
                    }}</a-menu-item>

                  <a-menu-item
                      key="partDocumentTemp"
                      @click="openImportModal('PartTemp')"
                  >{{ $t("导入部件数据") }}</a-menu-item
                  >
                  <a-menu-item
                      key="documentTemp"
                      @click="openImportModal('DocumentTemp')"
                  >{{ $t("导入文档数据") }}</a-menu-item>
                  <a-menu-item
                      key="PartLink"
                      @click="openImportModal('PartLink')"
                  >{{ $t("导入关联关系") }}</a-menu-item>
                  <a-menu-item
                      key="batchImportBOM"
                      @click="openImportModal('batchImportBOM')"
                  >{{ $t("批量导入BOM") }}</a-menu-item>
                  <a-menu-item
                      key="batchImportGLOBAL"
                      @click="openImportModal('batchImportGLOBAL')"
                  >{{ $t("批量导入全局替代") }}</a-menu-item>
                </a-menu>
                <a-button>
                  <jw-icon type="jwi-iconImport" />
                </a-button>
              </a-dropdown>
              <a-dropdown :disabled=!isAdmin placement="bottomRight">
                <a-menu slot="overlay">
                  <a-menu-item
                      key="partDocumentTemp"
                      @click="exportPart"
                  >{{ $t("btn_export") + $t("txt_part") }}</a-menu-item
                  >
                  <a-menu-item
                      key="documentTemp"
                      @click="exportDocument"
                  >{{ $t("导出文档") }}</a-menu-item
                  >
                  <a-menu-item
                      key="contentExcel"
                      @click="exportContentExcel"
                  >{{ $t("导出文档清单") }}</a-menu-item
                  >

                </a-menu>
                <a-button :loading="exportLoading">
                  <jw-icon type="jwi-iconexport" />
                </a-button>
              </a-dropdown>
              </template>
            <template #thumbSlot="{ row }">

              <!-- 2D图类型 -->
              <div
                  class="thumb-view"
                  :title="$t('txt_filePreview')"
                  @click="onPreview(row)"
                  v-if="valid2d(row.modelDefinition)"
              >
                <jwIcon type="#jwi-PDFwendang" />
              </div>
              <!-- 3D图类型 -->
              <cad-thumbnail
                  v-else-if="valid3d(row.modelDefinition,row.type)"
                  :currentRow="row"
              ></cad-thumbnail>
              <!-- 项目产品文档化 -->
              <div class="thumb-view" v-else-if="validBorrow(row)">
                <jwIcon type="#jwi-tihuan" />
              </div>
            </template>
            <template #numberSlot="{ row }">
              <router-link
                  :class="`${
                  getDeactivate(row.lifecycleStatus) ? 'invalid-materials' : ''
                }`"
                  target="_blank"
                  :to="{
                  path: '/detailPage',
                  query: {
                    oid: row.oid,
                    type: row.type,
                    masterType: row.masterType,
                    modelDefinition: row.modelDefinition,
                    tabActive: 'product',
                  },
                }"
              >
                <jwIcon :type="row.modelIcon" />
                {{ row.number }}
              </router-link>
            </template>

            <template #nameSlot="{ row }">
              <span>
                {{ row.cname || row.name }}
              </span>
            </template>
            <template #updateDate="{ row }">
              <span>
                {{ formatDateFn(row.updateDate) }}
              </span>
            </template>
            <template #isLock="{ row }">
              <a-tooltip>
                <template slot="title">
                  {{ row.lockOwnerAccount }} {{ $t("txt_check_out") }}
                </template>
                <jw-icon
                    v-if="row.lockOwnerOid"
                    :type="
                    row.lockOwnerOid === jwUser.oid
                      ? '#jwi-beiwojianchu'
                      : '#jwi-bierenjianchu'
                  "
                />
              </a-tooltip>
            </template>
            <template #loadStatus="{ row }">
              <jw-icon v-if="!row.loadStatus" type="#jwi-liuchengzhong" />
            </template>
            <template #versionSlot="{ row }">
              <span>
                {{ row.displayVersion }}
              </span>
            </template>
            <template #operation="{ row }">
              <operation-dropdown
                  :current-record="row"
                  @complete="reFetchData"
                  @fetchTable="fetchTable"
                  :currentTree="currentTree"
                  :location="selectLocation"
                  :addIterationAble="addIterationAble"
                  :treeName="treeNameAll"
                  :containerOid="$route.query.oid"
                  :containerModel="$route.query.modelDefinition"
                  :containerType="$route.query.type"
                  @batchOperator="batchOperator"
              >
              </operation-dropdown>
            </template>
            <template #owner="{ row }">
              <user-info :accounts="[row.owner]" :showname="true"/>
            </template>
          </jw-table>
          <card-list
              v-if="switchType"
              :currentTree="currentTree"
              :tableData="tableData"
              :treeName="treeNameAll"
              :pagination="pagerConfig"
              @reloadData="reFetchData"
              @onPageChange="onPageChange"
              @onSizeChange="onSizeChange"
              @batchOperator="batchOperator"
          />
        </a-spin>
      </div>
    </div>

    <create-drawer ref="cerateDrawer" @fetchTable="fetchTable"></create-drawer>
    <a-modal
        v-model.trim="addFoldVisible"
        :title="$t('txt_create_folder')"
        width="240px"
        :mask="false"
        :getPopupContainer="(treeNode) => treeNode"
        :wrapClassName="'rename-Modal'"
        @cancel="cancelFold"
        destroyOnClose
    >
      <a-form-model
          ref="addFoldParams"
          :model="addFoldParams"
          :rules="createRules"
      >
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-model-item :label="$t('txt_folder_name')" prop="name">
              <a-input
                  allowClear
                  v-model.trim="addFoldParams.name"
                  v-decorator="[
                  'name',
                  {
                    rules: [createRules.name],
                  },
                ]"
                  :maxLength="100"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item :label="$t('分系统/单机号')" prop="extensionContent.code">
              <a-input
                allowClear
                v-model.trim="addFoldParams.extensionContent.code"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <template slot="footer">
        <a-button
            key="submit"
            type="primary"
            :loading="createLoading"
            @click="createFold"
        >
          {{ $t("txt_add_c") }}
        </a-button>
        <a-button key="back" @click="cancelFold"
        >{{ $t("btn_cancel") }}
        </a-button>
      </template>
    </a-modal>
    <a-modal
        v-model.trim="renameVisible"
        :title="$t('编辑文件夹')"
        width="1000px"
        :mask="false"
        :getPopupContainer="() => document.getElementById('renameBtn')"
        @ok="renameVisible = false"
        :wrapClassName="'rename-Modal'"
        destroyOnClose
    >
      <a-form-model ref="renameForm" :model="renameForm" :rules="renameRules">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_name')" prop="renameValue">
              <a-input
                  allowClear
                  v-model.trim="renameForm.renameValue"
                  v-decorator="[
                  'renameValue',
                  {
                    rules: [
                      { required: true, message: $t('placeholder_name') },
                    ],
                  },
                ]"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item :label="$t('分系统/单机号')" prop="extensionContent.code">
              <a-input
                allowClear
                v-model.trim="renameForm.extensionContent.code"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="24">
            <team-manager :folder="currentTree" v-if="renameVisible"></team-manager>
          </a-col>
        </a-row>
      </a-form-model>
      <template slot="footer">
        <a-button
            key="submit"
            type="primary"
            :loading="renameLoading"
            @click="renameOpration"
        >
          {{ $t("btn_done") }}
        </a-button>
        <a-button key="back" @click="renameVisible = false"
        >{{ $t("btn_cancel") }}
        </a-button>
      </template>
    </a-modal>
    <download-modal
        :visible="visibleDown"
        :currentTree="currentTree"
        @close="onCloseDownModal"
        @getList="fetchFold"
    >
    </download-modal>
    <import-modal
        :visible="visibleExport"
        :currentTree="currentTree"
        @showDown="visibleDown = true"
        @close="visibleExport = false"
        @getList="fetchTable"
    >
    </import-modal>
    <batch-import-modal
        :visible="batchImportVisible"
        :currentTree="currentTree"
        @close="batchImportVisible = false"
        @getList="reFetchData"
    >
    </batch-import-modal>

    <!-- 带附件导入导出zip -->
    <batchImportModelFile
        :objInfo="modelObjInfo"
        :visible="importModelFileVisible"
        :currentTree="currentTree"
        @close="importModelFileVisible = false"
        @getList="reFetchData"
    >
    </batchImportModelFile>

    <!-- 导入部件和结构及有效性 -->
    <import-modal-partbom
        :visible="visiblePartBomExport"
        :currentTree="currentTree"
        @close="visiblePartBomExport = false"
        @getList="reFetchData"
    />

      <!-- 创建文件夹 -->
    <folder-create-from :currentTree="currentTree" ref="folderCreateFrom" @reloadTree="fetchFold">

    </folder-create-from>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory"
import { jwSimpleTabs, jwAvatar, jwIcon, jwModalForm } from "jw_frame"
import { formatDate } from "jw_utils/moment-date"
import createDrawer from "./create-drawer"
import rules from "../../../utils/rules"
import { getExcelList } from "./apis/index"
import cardList from "./card-list.vue"
import operationDropdown from "components/operation-dropdown"
import downloadModal from "./download-modal.vue"
import importModal from "./import-modal.vue"
import batchImportModal from "./batch-import-modal.vue"
import batchImportModelFile from "./batch-import-model-file.vue"
import { getChainParent } from "utils/util"
import { getCookie } from "jw_utils/cookie"
import cadThumbnail from "components/cad-thumbnail.vue"
import commonStore from "jw_stores/common"
import BatchOperator from "../../batch-operator-dialog/batch-operator.vue"
import ImportModalPartbom from "./import-modal-partbom.vue"
import FolderBtn from "./components/folder-btn.vue"
import teamManager from "../../team-manager";
import { getDropdownList } from "/apis/part/index";
import FolderCreateFrom from './components/folder-create-from.vue'
import userInfo from "components/user-info";
import VueDragResize from 'vue-drag-resize'

// 文件夹目录
const fetchfolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/pdm-folder/searchTree`,
  method: "get",
})

const initUserPermission = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/v1/permission/policy/initPermission`,
  method: "get",
})

// const fetchfolderTree = ModelFactory.create({
//   url: `${Jw.gateway}/container/folder/searchTree`,
//   method: "get",
// })

// 新增文件夹
const createFolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/pdm-folder/create`,
  method: "post",
})

// 查询目录下的内容

// const fetchContent = ModelFactory.create({
//   url: `${Jw.gateway}/${Jw.containerService}/folder/fuzzySubPage`,
//   method: "post",
// })

const fetchContent = ModelFactory.create({
  // url: `${Jw.gateway}/${Jw.containerService}/pdmfolder/pdmfuzzySubPage`,
  url: `${Jw.gateway}/${Jw.customerServer}/pdm-folder/pdmfuzzySubPageWithModel`,
  method: "post",
})

// 查询部件，文档，CAD权限
const getOperationFilter = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post",
})

// 查询文件夹是否有添加和删除权限
const getFolderFilter = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post",
})

// 重命名文件夹
const renameFolder = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/pdm-folder/update`,
  method: "post",
})

const previewApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.fileMicroServer}/file/getUrlByOidForPreview`,
  method: "get",
})



export default {
  components: {
    jwModalForm,
    jwSimpleTabs,
    jwAvatar,
    jwIcon,
    createDrawer,
    cardList,
    operationDropdown,
    downloadModal,
    importModal,
    batchImportModal,
    cadThumbnail,
    BatchOperator,
    ImportModalPartbom,
    batchImportModelFile,
    FolderBtn,
    teamManager,
    FolderCreateFrom,
    userInfo,
    VueDragResize
  },
  data() {
    return {
      isAdmin:false,
      renameDataRef: null,
      batchMcadVisible: false,
      addIterationAble:false,
      selectLocation:{},
      noChildNode:[],
      //是否显示树
      treeshow: true,

      selectedKeys: [],
      // 导入导出
      visibleExport: false,
      visibleDown: false,
      exportLoading: false,
      // 文件夹重命名
      renameForm: {
        renameValue: "",
        extensionContent: {},
      },
      renameLoading: false,
      renameVisible: false,
      renameRules: {
        renameValue: [
          { required: true, message: this.$t("msg_input"), trigger: "change" },
        ],
      },
      // 全部数据配置
      subTypes: [
        "PartIteration",
        "DocumentIteration",
        "MCADIteration",
        "ECADIteration",
      ],
      subTypesTitle: this.$t("txt_all_datas"),
      // 当前选择的modelDefinition
      currentModelDefinition: null,
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0,
      }, //分页配置
      cardSpinning: false,
      switchType: false, //  切换卡片和表格形式
      // tab选项
      tabValue: "product",
      tabOption: [
        { value: "product", label: this.$t("txt_content_management") },
        { value: "resource", label: this.$t("txt_product_structure") },
        { value: "provider", label: this.$t("txt_change_management") },
        { value: "provider", label: this.$t("txt_baseline_management") },
      ],
      jwUser: Jw.getUser(),
      // 新增团队表单信息
      currentTree: {},
      treeNameAll: "",
      rules,
      createLoading: false,
      createRules: {
        name: [
          { required: true, message: this.$t("txt_input"), trigger: "change" },
        ],
      },
      addFoldVisible: false,
      addFoldParams: {
        name: "",
        description: "",
        extensionContent: {},
      },
      // 带搜索树形结构
      replaceFields: {
        key: "oid",
        title: "name",
        children: "children",
      },
      expandedKeys: [],
      searchValue: "",
      autoExpandParent: true,
      // 标题数据源
      contentTreeData: [],
      // 表格状态
      searchKey: "",
      // tableData: [
      //   {name:'test',lifecycleOid:1,lifecycleStatus:'审核中'},
      //   {name:'test',lifecycleOid:2,lifecycleStatus:'工作中'},
      //   {name:'test',lifecycleOid:1,lifecycleStatus:'审核中'},
      // ],
      tableData:[],
      selectedRows: [],
      confirmLoadingStatus: false,
      pagination: {
        page: 1,
        size: 10,
        total: 30,
      },
      tableLoading: false,
      total: 0,
      selectRow: [],

      // 部件操作权限
      permissionList: [],
      // 文件夹操作权限
      folderRole: false,

      importModelFileVisible: false,
      batchImportVisible: false,

      batchImportePartVisible: false,
      visiblePartBomExport: false,

      modelObjInfo: {},
      currentSelectedNode: null,
    }
  },
  computed: {
    getHeader() {
      return [
        {
          field: "isLock",
          title: "",
          params: {
            showHeaderMore: false,
          },
          align: "center",
          width: 38,
          slots: {
            default: "isLock",
          },
        },
        {
          field: "thumbnailOid",
          title: "",
          params: {
            showHeaderMore: false,
          },
          width: "40px",
          className: "thumbSlotclass",
          slots: {
            default: "thumbSlot",
          },
        },
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true,
          slots: {
            default: "numberSlot",
          },
          width: "250px",
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          sortable: true,
          slots: {
            default: "nameSlot"
          },
          width: "250px",
        },
        {
          field: "extensionContent.cn_jwis_gg",
          title: this.$t("规格"),
        },
        // {
        //   field: "modelDefinition",
        //   title: this.$t("txt_type"),
        //   sortable: true,
        // },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          sortable: true,
        },
        {
          field: "displayVersion",
          title: this.$t("txt_plan_version"),
          sortable: true,
          slots: {
            default: "versionSlot",
          },
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_date"),
          sortable: true, // 开启排序
          width: 150,
          slots: {
            default: "updateDate",
          },
        },
        {
          field: "owner",
          title: this.$t("txt_owner"),
          sortable: true, // 开启排序
          slots: {
            default: 'owner',
          }
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          slots: {
            default: "operation",
          },
        },
      ]
    },
    toolbars() {
      let hasChildren = this.currentSelectedNode && this.currentSelectedNode.children && this.currentSelectedNode.children.length > 0;
      let customToolBar = [
        {
          name: this.$t("txt_part"),
          position: "before",
          type: "default",
          key: "Part",
          prefixIcon: "#jwi-lianjian",
          // isVisible: this.permissionList.includes("Part.create"),
          isVisible:  !hasChildren && this.permissionList.includes("Part.create"),
        },
        {
          name: this.$t("txt_document"),
          position: "before",
          type: "default",
          key: "Document",
          prefixIcon: "#jwi-wendang",
          // isVisible: this.permissionList.includes("Document.create"),
          isVisible:  !hasChildren && this.permissionList.includes("Document.create"),
        },
        /*{
          name: this.$t("软件配置项"),
          position: "before",
          type: "default",
          key: "SCI",
          prefixIcon: "#jwi-ruanjian",
          isVisible:  !hasChildren && this.permissionList.includes("Document.create"),
        },*/
        // {
        //   name: "ECAD",
        //   position: "before",
        //   type: "default",
        //   key: "ECAD",
        //   prefixIcon: "#jwi-wendang",
        //   isVisible:
        //     this.$route.query.modelDefinition == "ResourceContainer" &&
        //     this.permissionList.includes("ECAD.create"),
        // },
        {
          name: this.subTypesTitle,
          display: "dropdown",
          position: "before",
          class: "sub-types-title",
          key: "allData",
          menuList: [
            {
              name: this.$t("txt_all_datas"),
              key: "all",
            },
            {
              name: this.$t("txt_part"),
              key: "PartIteration",
            },
            {
              name: this.$t("txt_document"),
              key: "DocumentIteration",
            },
            {
              prefixIcon: "jwi-search",
              name: this.$t("txt_structure_CAD"),
              key: "MCADIteration",
            },
            {
              name: this.$t("txt_electronic_CAD"),
              key: "ECADIteration",
            },
            {
              name: "零件",
              key: "MCADIteration_CADPart",
            },
            {
              name: "工程图",
              key: "MCADIteration_CADDrawing",
            },
            {
              name: "装配",
              key: "MCADIteration_CADAssembly",
            },
          ],
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: false,
          placeholder: this.$t("search_text"),
          prefixIcon: "jwi-search",
          key: "search",
        },
      ]
      return customToolBar
    },
    getDeactivate(lifecycleStatus) {
      return (lifecycleStatus) => {
        return Jw.deactivate ? Jw.deactivate.includes(lifecycleStatus) : false
      }
    },
  },

  watch: {
    currentRecord: {
      deep: true,
      handler(val) {},
    },
    modalSelectedRows(val) {},
    switchType(){
      this.selectedRows=[]
    },
    tableData(){
      //this.selectedRows = []
      const t0 = this.$refs.ref_table.getCheckboxReserveRecords(),
          t1 = this.$refs.ref_table.getCheckboxRecords();

      this.selectedRows = [...t0, ...t1];
    }
  },
  created() {
    // 输入回调去抖动
    this.delaySearch = _.debounce(this.onSearch, 500)
    this.fetchFold()
    // this.operationFilter()
    this.getFolderRole()
    // this.initBreadcrumb();
    // window.localStorage.setItem('deep',true)
    console.log(this.$route,'sddd')
  },

  methods: {
    onCheckboxChange(){
      const t0 = this.$refs.ref_table.getCheckboxReserveRecords(),
          t1 = this.$refs.ref_table.getCheckboxRecords();

      this.selectedRows = [...t0, ...t1];
    },
    checkAdmin() {
      this.isAdmin = Jw.getUser().systemAdmin || Jw.getUser().dataAdmin || Jw.getUser().tenantAdmin;
    },
    initUserPermission(){
      initUserPermission
          .execute()
          .then(data => {})
          .catch(err => {
            if (err.msg) {
              this.$error(err.msg);
            }
          });
      this.checkAdmin();
    },
    // 根据已有文件夹创建
    createByUse(treeData){
      this.currentTree = {...treeData}
      this.$refs['folderCreateFrom'].show()
    },
    openImportModal(type) {
      this.modelObjInfo = getExcelList(type)
      this.importModelFileVisible = true
    },
    //2d文档显示
    valid2d(modelDefinition) {
      return (
          modelDefinition == "CADDrawing" ||
          modelDefinition == "PCB" ||
          modelDefinition == "Schematic" ||
          modelDefinition == "CADLayout" ||
          modelDefinition == "CAD2DSketch" ||
          modelDefinition == "CADCEDrawing"||
          modelDefinition == 'MentorProject'
      )
    },
    //3d图显示
    valid3d(modelDefinition,type) {
      return type != "DocumentIteration"
    },
    //项目文档产品化
    validBorrow(row){
      return row.borrow
    },
    batchOperator(record, type) {
      this.selectedRows = [record]
      this.$nextTick(() => {
        this.$refs["batch-operator"].validSelect(type)
      })
    },
    onPreview(row){
      let viewCode = this.getViewCode(row.masterType)
        getDropdownList.execute({viewCode,objectOid: row.oid}).then(resp => {
          let detail = resp.find(row => row.code === "details")
          if(detail.status === 'enable'){
            //跳转
            this.jumpDetailFun(row)
          }else{
            this.$error("无查看详情权限")
          }
        })
    },
    jumpDetailFun(row) {
      previewApi
          .execute({
            fileOid: row.thumbnailOid,
          })
          .then((url) => {
            commonStore.set("query", url)
            window.open("#/preview", "_blank")
          })
          .catch((err) => {
            if (err.code === -1) {
              this.$error(this.$t("没有可预览的文件"))
            }
          })
          .finally(() => {})
    },

    getViewCode(masterType){
      let viewCode = ""
      switch (masterType) {
          case "Document":
            viewCode = "DOCUMENTINSTANCE";
            break;
          case "Part":
            viewCode = "PARTINSTANCE";
            break;
          case "MCAD":
            viewCode = "MCADDOCUMENTINSTANCE";
            break;
          case "ECAD":
            viewCode = "ECADINSTANCE";
            break;
          case "PCB":
            viewCode = "PARTINSTANCE";
            break;
          case "Schematic":
            viewCode = "PARTINSTANCE";
            break;
          case "Datasheet":
            viewCode = "PARTINSTANCE";
            break;
          case "Symbol":
            viewCode = "PARTINSTANCE";
            break;
          case "Encapsulation":
            viewCode = "PARTINSTANCE";
            break;
          case "DocumentTemplateMaster":
            viewCode = "DOCUMENTTEMPLATEINSTANCE";
            break;
        }
        return viewCode;
    },
    // 显示重命名文件夹模态框
    showRename(dataRef) {
      this.renameDataRef = dataRef
      this.renameForm.renameValue = dataRef.name
      this.renameForm.extensionContent = dataRef.extensionContent ? dataRef.extensionContent : {}
      this.renameVisible = true
    },
    renameOpration() {
      let { renameForm, renameDataRef } = this
      let param = {
        name: renameForm.renameValue,
        oid: renameDataRef.oid,
        extensionContent: renameForm.extensionContent,
      }
      this.renameLoading = true
      renameFolder
          .execute(param)
          .then((data) => {
            // this.fetchFold()
            this.renameVisible = false
            this.renameLoading = false
            this.$success(this.$t("msg_success"))
          })
          .catch((err) => {
            console.error(err)
            this.renameLoading = false
            this.$error(err.msg || this.$t("msg_failed"))
          })
    },
    gotoDetails(row) {
      Jw.jumpToDetail(row, { tabActive: "info" })
    },
    // 查询文件夹权限
    getFolderRole() {
      let { oid } = this.$route.query
      let param = {
        viewCode: "CONTAINERINSTANCE",
        objectOid: oid,
      }
      getFolderFilter
          .execute(param)
          .then((data) => {
            let editRole = data.filter((item) => item.code === "edit")[0]
            this.folderRole = editRole.status === "enable" ? true : false
          })
          .catch((err) => {
            console.error(err)
            this.$error(err.msg || this.$t("msg_failed"))
          })
    },
    // 查询部件，文档，CAD权限
    operationFilter(FolderOid) {
      this.permissionList = []
      let { oid } = this.$route.query
      let param = null
      if (FolderOid) {
        param = {
          viewCode: "NOINSTANCE",
          objectType: "Folder",
          objectOid: FolderOid,
        }
      } else {
        param = {
          viewCode: "NOINSTANCE",
          objectType: "Container",
          objectOid: oid,
        }
      }

      getOperationFilter
          .execute(param)
          .then((data) => {
            if (data) {
              data.forEach((item) => {
                if (item.status == "enable") {
                  this.permissionList.push(
                      item.modelType + "." + item.permissionKey
                  )
                }
              })
            }
          })
          .catch((err) => {
            console.error(err)
            this.$error(err.msg || this.$t("msg_failed"))
          })
    },

    // 删除文件夹
    deleteFolder(treeKey, treeRow) {
      let { oid } = treeRow
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
            <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
              {this.$t("btn_delete")}
            </p>
        ),
        content: (
            <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">
              {this.$t("txt_is_delete_foleder")}
            </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_confirm"),
        onOk: () => {
          ModelFactory.create({
            url: `${Jw.gateway}/${Jw.containerService}/folder/delete/${oid}`,
            method: "post",
          })
              .execute()
              .then((data) => {
                this.$success(this.$t("txt_delete_success"))
                //刷新列表
                this.fetchFold()
              })
              .catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"))
              })
        },
      })
    },
    // 文件夹新增方法处理
    createFold() {
      let { addFoldParams } = this
      let { currentTree } = this
      let { name, description, extensionContent } = addFoldParams
      let containerObj = currentTree
      let param = {
        name,
        description,
        locationInfo: {
          catalogOid: containerObj.oid,
          catalogType: containerObj.type,
          containerOid: containerObj.containerOid,
          containerType: containerObj.containerType,
        },
        extensionContent
      }
      this.$refs.addFoldParams.validate((valid) => {
        if (valid) {
          this.createLoading = true
          createFolderTree
              .execute(param)
              .then((data) => {
                this.fetchFold()
                this.createLoading = false
                this.addFoldVisible = false
              })
              .catch((err) => {
                this.tableLoading = false
                this.createLoading = false
                this.$error(err.msg || this.$t("msg_failed"))
              })
              .finally(() => {
                this.addFoldParams.name = undefined
                this.addFoldParams.extensionContent = {}
              })
        } else {
          this.$error("error submit!!")
          return false
        }
      })
    },
    cancelFold() {
      this.addFoldParams.name = undefined
      this.addFoldParams.extensionContent = {}
      this.addFoldVisible = false
    },
    // 文件夹右键选择菜单
    showAddFolder(treeKey, treeRow) {
      this.addFoldVisible = true
      this.currentTree = { ...treeRow }
    },
    // 扁平化树形结构数组
    flatten(array) {
      var flattend = []
      ;(function flat(array) {
        array.forEach(function (el) {
          for (let i in el) {
            if (Object.prototype.toString.call(el[i]) === "[object Array]") {
              flat(el[i])
            }
          }
          flattend.push(el)
        })
      })(array)
      return flattend
    },
    deepData(data, scopedSlots) {
      let _this = this
      data.map((item, index) => {
        item.title = item.name
        item.key = item.oid
        item.value = item.oid
        if (scopedSlots) {
          item.scopedSlots = scopedSlots
          if (scopedSlots.title) delete item.title
        }
        if (item.children && item.children.length > 0) {
          item.children.map((item) => (item.childType = "child"))
          _this.deepData(item.children, scopedSlots)
        } else {
          this.noChildNode.push(item.oid)
        }
      })
      return data
    },
    // 获取文件夹树形目录结构
    fetchFold() {
      this.initUserPermission();
      let { query } = this.$route
      let param = {
        containerOid: query.oid,
        containerModel:
            query.masterType || query.modelDefinition || query.containerModel,
      }
      fetchfolderTree
          .execute(param)
          .then((data) => {
            /**
             * @param treeName
             *
             * 从地址栏获取treeName，
             * 如果存在/，需要展开并且选中/后面的文件夹
             * 如果只存在顶级节点，则加载顶级节点文件夹
             *
             * */
            this.noChildNode = []
            this.contentTreeData = this.deepData(data)
            let defaultExpandedKeys = data[0].oid
            localStorage.setItem("contenttreelocation", defaultExpandedKeys)
            let flattenData = this.flatten(data)
            let routeTreeOid = this.$route.query.treeOid
            let routeTree = flattenData.filter(
              (item) => item.oid === routeTreeOid
            )[0]
            if (routeTree) {
              this.selectedKeys = [routeTree.oid]
              this.expandedKeys = [routeTree.oid]
              this.fetchTable(routeTree)
              if (data.length > 0) {
                this.currentTree = routeTree
              }
              this.currentSelectedNode = routeTree;
              this.operationFilter(routeTree.oid)
            } else {
              this.fetchTable(this.deepData(data)[0])

              if(!this.expandedKeys.length){
                this.expandedKeys = [defaultExpandedKeys]
              }
              if (data.length > 0 && !this.currentTree.oid) {
                this.currentTree = data[0]
                this.selectedKeys = [this.currentTree.oid]
                this.currentSelectedNode = data[0];
                this.operationFilter(this.currentTree.oid)
              }
            }
          })
          .catch((err) => {
            console.error(err)
            this.tableLoading = false
            this.cardSpinning = false
            this.$error(err.msg || this.$t("msg_failed"))
          })
    },
    filterRouteName() {
      if (
          this.$route.query.treeName &&
          this.$route.query.treeName.lastIndexOf("/") > 0
      ) {
        /**
         *
         * 判断是否有下划线/
         * 如果有下划线 匹配当前树的数据
         * 触发树选择事件
         *
         * */
        let treeName = this.$route.query.treeName
        let currentNameIndex = treeName.lastIndexOf("/") + 1
        let currentName = treeName.substring(currentNameIndex, treeName.length)
        return currentName
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    onSelect(value, node, extra) {
      let { dataRef } = node.node
      this.currentTree = dataRef
      this.selectLocation={catalogOid:this.currentTree.oid,catalogType:this.currentTree.type}
      localStorage.setItem("contenttreelocation", this.currentTree.oid)
      this.pagerConfig = {
        current: 1,
        pageSize: 20,
        total: 0,
      }
      this.selectedKeys = [dataRef.oid]
      this.operationFilter(dataRef.oid)
      this.fetchTable(dataRef)
      this.currentSelectedNode = dataRef; // 更新当前选中的树节点
    },
    tabChange(option) {
      let { value } = option
      this.tabValue = value
      this.fetchTable({ current: 1, pageSize: 20 })
    },
    // 选择列回调
    onSelectChange(args) {
      this.selectRow = args
    },
    // 操作列回调
    onOperateClick(key, row) {
      if (key === "lanuch") {
        ///
      } else if (key === "delete") {
        this.onDelete(row)
      }
    },
    // 工具栏点击回调
    onToolClick(item) {
      if (item.key === "create") {
      } else if (item.key === "delete") {
        this.fetchDelete(this.selectedRows)
      } else if (item.key === "Part") {
        this.$refs.cerateDrawer.show({
          title: this.$t("btn_new_create") + item.name,
          modelInfo: {
            layoutName: "create",
            modelName: "Part",
          },
          params: {
            url: `${Jw.gateway}/${Jw.customerServer}/part/create`,
            locationInfo: {
              catalogOid: this.currentTree.oid,
              catalogType: this.currentTree.type,
              containerOid: this.$route.query.oid,
              containerType: this.$route.query.type,
              containerModelDefinition: this.$route.query.modelDefinition,
            },
          },
          btnType: "Part",
        })
      } else if (item.key === "Document") {
        this.$refs.cerateDrawer.show({
          title: this.$t("btn_new_create") + item.name,
          modelInfo: {
            layoutName: "create",
            modelName: "Document",
          },
          params: {
            url: `${Jw.gateway}/${Jw.customerServer}/${Jw.docMicroServer}/document/create`,
            locationInfo: {
              disabled:true,
              catalogOid: this.currentTree.oid,
              catalogType: this.currentTree.type,
              containerOid: this.$route.query.oid,
              containerType: this.$route.query.type,
              containerModelDefinition: this.$route.query.modelDefinition,
            },
          },
          btnType: "Document",
        })
      } else if (item.key === "SCI") {
        this.$refs.cerateDrawer.show({
          title: this.$t("btn_new_create") + item.name,
          modelInfo: {
            layoutName: "create",
            modelName: "SCI",
          },
          params: {
            url: `${Jw.gateway}/${Jw.customerServer}/${Jw.docMicroServer}/document/create`,
            locationInfo: {
              disabled:true,
              catalogOid: this.currentTree.oid,
              catalogType: this.currentTree.type,
              containerOid: this.$route.query.oid,
              containerType: this.$route.query.type,
              containerModelDefinition: this.$route.query.modelDefinition,
            },
          },
          btnType: "SCI",
        })
      } else if (item.key === "ECAD") {
        this.$refs.cerateDrawer.show({
          title: this.$t("btn_new_create") + item.name,
          modelInfo: {
            layoutName: "create",
            modelName: "ECAD",
          },
          params: {
            url: `${Jw.gateway}/${Jw.cadService}/ecad/create`,
            locationInfo: {
              catalogOid: this.currentTree.oid,
              catalogType: this.currentTree.type,
              containerOid: this.$route.query.oid,
              containerType: this.$route.query.type,
              containerModelDefinition: this.$route.query.modelDefinition,
            },
          },
          btnType: "ECAD",
        });
      } else if (item.key === "switch") {
        // 切换列表
        let {switchType} = this;
        this.switchType = !switchType;
        let vxeTable = document.querySelector("div.vxe-table");
        let pagerWrapper = document.querySelector("div.vxe-grid--pager-wrapper");
        if (switchType) {
          vxeTable.style.display = "block"
          pagerWrapper.style.display = "block"
        } else {
          vxeTable.style.display = "none"
          pagerWrapper.style.display = "none"
        }
        this.reFetchData();
      } else if (
          item.key === "all" ||
          item.key === "PartIteration" ||
          item.key === "DocumentIteration" ||
          item.key === "MCADIteration" ||
          item.key === "ECADIteration" ||
          item.key === "MCADIteration_CADPart" ||
          item.key === "MCADIteration_CADDrawing" ||
          item.key === "MCADIteration_CADAssembly"
      ) {
        this.subTypesTitle = item.name;
        if (item.key === "all") {
          this.subTypes = [
            "PartIteration",
            "DocumentIteration",
            "MCADIteration",
            "ECADIteration",
          ]
          this.currentModelDefinition = null;
        } else if (item.key === "MCADIteration_CADPart") {
          this.subTypes = ["MCADIteration"]
          this.currentModelDefinition = "CADPart";
        } else if (item.key === "MCADIteration_CADDrawing") {
          this.subTypes = ["MCADIteration"]
          this.currentModelDefinition = "CADDrawing";
        } else if (item.key === "MCADIteration_CADAssembly") {
          this.subTypes = ["MCADIteration"]
          this.currentModelDefinition = "CADAssembly";
        } else {
          this.subTypes = [item.key]
          this.currentModelDefinition = null;
        }
        this.pagerConfig = {
          current: 1,
          pageSize: 20,
          total: 0,
        };
        this.reFetchData();
      }
    },
    // 工具栏输入回调
    onToolInput({ key }, value) {
      if (key === "search") {
        this.searchKey = value
        this.pagerConfig = {
          current: 1,
          pageSize: 20,
          total: 0,
        }
        this.delaySearch()
      }
    },

    onCloseDownModal() {
      this.visibleDown = false
    },

    // 导出part数据
    exportPart() {
      let { selectedRows, currentTree, $route, subTypes } = this
      let oidList = selectedRows.map((item) => item.oid)
      const accesstoken = getCookie("token")
      this.exportLoading = true
      fetch(
          `${Jw.gateway}/${Jw.customerServer}/${Jw.partBomMicroServer}/part-export/export-part-info`,
          {
            method: "post",
            body: JSON.stringify({
              tenantOid: getCookie("tenantOid"),
              fromOid: currentTree.oid,
              fromType: currentTree.type,
              containerName: $route.query.containerName,
              oidList: oidList,
              // subTypes,
            }),
            headers: {
              "Content-Type": "application/json;charset=utf8",
              appName: Jw.appName,
              accesstoken,
              tenantAlias: getCookie("tenantAlias"),
              tenantOid: getCookie("tenantOid"),
            },
          }
      )
          .then((response) => {
            return response.blob()
          })
          .then((data) => {
            this.$success(this.$t("txt_export_success"))
            this.downBlob(data,currentTree.name,'xlsx')
            this.exportLoading = false
          })
          .catch((err) => {
            console.error(err)
            this.exportLoading = false
            this.$error(err.msg || this.$t("msg_failed"))
          })
    },
    //导出文档
    exportDocument(){
      let oidList = this.selectedRows.map(item => item.oid);
      let subTypes = [
          "DocumentIteration",
          "MCADIteration",
          "ECADIteration"
        ];
      let { current, pageSize } = this.pagerConfig;
      let param = {
        subTypes,
        fromOid: this.currentTree.oid, // 目录/容器oid
        fromType: "Folder", // 目录/容器类型
        index: current, // 分页页数
        size: pageSize,
        searchKey:this.searchKey||"",
        oidList:oidList
      };
      const accesstoken = getCookie("token");
      this.exportLoading = true;
      fetch(
        `${Jw.gateway}/customer/customerContainer/exportDocument`,
        {
          method: "post",
          body: JSON.stringify(param),
          headers: {
            "Content-Type": "application/json;charset=utf8",
            appName: Jw.appName,
            accesstoken,
            tenantAlias: getCookie("tenantAlias"),
            tenantOid: getCookie("tenantOid")
          }
        }
      )
        .then(response => {
          return response.blob();
        })
        .then(data => {
          this.$success(this.$t("txt_export_success"));
          this.downBlob(data,this.currentTree.name)
          this.exportLoading = false;
        })
        .catch(err => {
          console.error(err);
          this.exportLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //只导出内容管理-Excel
    exportContentExcel(){
      let oidList = this.selectedRows.map(item => item.oid);
      let subTypes = [
        "DocumentIteration",
        "MCADIteration",
        "ECADIteration"
      ];
      let { current, pageSize } = this.pagerConfig;
      let param = {
        subTypes,
        fromOid: this.currentTree.oid, // 目录/容器oid
        fromType: "Folder", // 目录/容器类型
        index: current, // 分页页数
        size: pageSize,
        searchKey:this.searchKey||"",
        oidList:oidList
      };
      const accesstoken = getCookie("token");
      this.exportLoading = true;
      fetch(
          `${Jw.gateway}/customer/customerContainer/exportContentExcel`,
          {
            method: "post",
            body: JSON.stringify(param),
            headers: {
              "Content-Type": "application/json;charset=utf8",
              appName: Jw.appName,
              accesstoken,
              tenantAlias: getCookie("tenantAlias"),
              tenantOid: getCookie("tenantOid")
            }
          }
      )
          .then(response => {
            return response.blob();
          })
          .then(data => {
            this.$success(this.$t("txt_export_success"));
            this.downBlob(data,this.currentTree.name)
            this.exportLoading = false;
          })
          .catch(err => {
            console.error(err);
            this.exportLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    downBlob(data,name,fileType = 'zip'){
      let url = window.URL.createObjectURL(
          new Blob([data], {
            type: "application/zip;application/zip",
          })
      )
      let link = document.createElement("a")
      link.href = url
      link.setAttribute("download", `${name}.${fileType}`)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(url)
    },
    // 删除
    onDelete(row) {
      this.fetchDelete([row])
    },
    // 数据请求函数
    fetchTable(dataRef) {
      //判断当前是否允许创建部件文档等对象
      this.addIterationAble = this.noChildNode.includes(this.currentTree.oid)
      try {
        this.treeNameAll = getChainParent(
            this.contentTreeData,
            dataRef.oid,
            "oid",
            "/",
            true
        ).nameStr
      } catch (error) {
        console.error(error.message)
      }
      let { searchKey, subTypes, currentModelDefinition } = this
      let { current, pageSize } = this.pagerConfig
      let param = {
        subTypes,
        searchKey: searchKey.trim(), // 搜索词
        fromOid: dataRef.oid, // 目录/容器oid
        fromType: "Folder", // 目录/容器类型
        index: current, // 分页页数
        size: pageSize,
      }

      // 如果选择了特定的modelDefinition，添加到参数中
      if (currentModelDefinition) {
        param.modelDefinition = currentModelDefinition;
      }
      this.tableLoading = true
      this.cardSpinning = true
      return fetchContent
          .execute(param)
          .then((data) => {
            this.tableLoading = false
            this.cardSpinning = false
            this.tableData = data.rows
            this.pagerConfig.total = data.count
            this.total = data.count
            return { data: data.rows, total: data.count }
          })
          .catch((err) => {
            this.tableLoading = false
            this.cardSpinning = false
            this.$error(err.msg || this.$t("msg_failed"))
          })
    },
    initBreadcrumb() {
      // let breadcrumbData = [{ name: "IP策略管理", path: "/ipconfig" }];
      // this.setBreadcrumb(breadcrumbData);
    },
    // 删除列操作
    fetchDelete(row) {
      let param = row.map((item) => item.oid)
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
            <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
              {this.$t("btn_batch_delete")}
            </p>
        ),
        content: (
            <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">
              {this.$t("msg_system_del")}
            </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_confirm"),
        onOk: () => {
          deleteContainer
              .execute(param)
              .then((data) => {
                this.$success(this.$t("txt_delete_success"))
                //刷新列表
                this.fetchTable({ current: 1, pageSize: 20 })
              })
              .catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"))
              })
        },
      })
    },
    // 输入回调刷新表格数据
    onSearch() {
      // this.$refs.ref_table.reFetchData();
      this.tableLoading = true
      this.reFetchData()
    },
    // 时间格式化转换
    formatDateFn(date) {
      return formatDate(date)
    },
    //分页操作
    onPageChange(page, pageSize) {
      this.pages = {
        currentPage: page,
        size: pageSize,
        searchKey: this.searchKey,
      }

      this.pagerConfig.current = page
      this.pagerConfig.pageSize = pageSize
      this.reFetchData()
    },
    onSizeChange(pageSize, page) {
      this.pages = {
        currentPage: page,
        size: pageSize,
        searchKey: this.searchKey,
      }
      this.pagerConfig.current = page
      this.pagerConfig.pageSize = pageSize

      this.reFetchData()
    },
    reFetchData() {
      // this.$refs.ref_table.reFetchData();
      this.fetchTable({
        oid: this.currentTree.oid,
        type: this.currentTree.type,
      })
      // 清除表格选中状态
      this.clearTableSelect()
    },
    clearTableSelect(){
      const table = this.$refs.ref_table
      table && table.clearCheckboxReserve()
      table && table.clearCheckboxRow()
      this.selectedRows=[]
    }
  },
}
</script>
<style lang="less" scoped>
/deep/.jw-toolbar-right .ant-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}
/deep/.jw-tool-content.ant-btn
.jw-tool-content-icon.prefix-icon.jwi-iconlogo-windows-filled {
  margin-right: 0;
}
/deep/.jw-tool-content.ant-btn .jw-tool-content-icon.prefix-icon.jwi-iconlist {
  margin-right: 0;
}
/deep/.rename-Modal {
  /deep/.ant-modal {
    left: -15%;
  }
}
/deep/ .jw-page .ant-layout-content {
  overflow: hidden;
}
// tab页签样式
.wrap-class {
  /deep/.item-class {
    margin: 0 20px 0 0;
    padding: 4px 0;
    color: rgba(30, 32, 42, 0.65);
  }
  /deep/.item-class-active {
    font-size: 14px;
    font-weight: 500;
    color: rgba(30, 32, 42, 0.85);
  }
}

.product-content {
  height: calc(~"100% - 45px");
  padding-top: 0;
  padding-bottom: 0;
  box-shadow: none;
}
/deep/ .vdr-stick-mr{
  top: 0;
  height: 100% !important;
  margin-top: 0 !important;
  border-radius: 3px;
  background: #eee;
  border: 0px;
  right: -8px !important;
}
// 处理树形结构宽度不够
/deep/.ant-tree li .ant-tree-node-content-wrapper {
  width: 90% !important;
}
// 左侧菜单和右侧表格处理
.template-content {
  height: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  .hiddentreebtn {
    position: absolute;
    left: 210px;
    top: 8px;
    z-index: 55;
    cursor: pointer;
  }
  .template-title-border{
    border-right: 1px solid rgba(30, 32, 42, 0.15);
    background-color: #fff;
  }
  .template-title {
    padding-right: 12px;
    min-width: 230px;
    height: 100%;
    .title-search {
      margin-bottom: 8px;
      flex: 1;
      cursor: pointer;
    }
    .tree-title {
      margin: 20px 0;
      //   padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: nowrap;
      .title {
        flex: 3;
        font-size: 14px;
        color: rgba(30, 32, 42, 0.85);
      }
    }
    .title-item {
      margin-bottom: 2px;
      padding-left: 12px;
      height: 42px;
      line-height: 42px;
      font-size: 14px;
      color: rgba(30, 32, 42, 0.85);
      cursor: pointer;
      &:hover {
        background: rgba(30, 32, 42, 0.06);
        border-radius: 4px;
      }
    }
  }
  /deep/ .active:before{
    outline: 0;
  }

  /deep/ .active{
    border: 0px;
    border-right: 1px solid rgba(30, 32, 42, 0.15);
  }

  .template-list {
    height: 100%;
    width: 20px;
    padding: 10px 0 0 10px;
    flex: 1;
    .list-item {
      position: relative;
      margin-left: 16px;
      margin-bottom: 16px;
      float: left;
      width: 240px;
      height: 113px;
      padding: 20px;
      background: rgba(30, 32, 42, 0.04);
      border-radius: 4px 4px 0 4px 4px;
      border-radius: 4px 4px 0 4px 4px;
      cursor: pointer;
      &:hover {
        .item-hover {
          display: flex;
        }
      }
      .item-title {
        margin-bottom: 16px;
        font-size: 14px;
        color: rgba(30, 32, 42, 0.85);
      }
      .item-text {
        font-size: 12px;
        color: rgba(30, 32, 42, 0.45);
        text-align: justify;
      }
      .item-hover {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: none;
        align-items: center;
        justify-content: center;
        background: rgba(30, 32, 42, 0.45);
        border-radius: 4px 4px 0 4px 4px;
        border-radius: 4px 4px 0 4px 4px;
        .hover-primary {
          margin-right: 12px;
        }
      }
    }
  }
}
</style>

<style lang="less">
/deep/.ant-tree-node-content-wrapper {
  width: 90%;
}

.tree-self {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-width: 200px;
  .tree-self-title {
    margin-left: 4px;
    width: 65%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
  }
  .tree-self-opration-folder {
    margin-left: 8px;
    visibility: hidden;
  }
  &:hover {
    .tree-self-opration-folder {
      visibility: visible;
    }
  }
}
.template-list {
  .ant-spin-nested-loading,
  .ant-spin-container {
    height: 100%;
    .jw-toolbar-panel > div:first-child {
      .sub-types-title {
        justify-content: space-between;
        // width: 119px;
        // position: absolute;
        // left: 279px;
        z-index: 5;
        // border: 0;
        // padding: 0 8px;
        // margin-left: 12px;
        // height: 29px;
        // border-right: 1px solid #d9d9d9;
        border-right: 0;
        // border-radius: initial;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
      button + .sub-types-title {
        margin-right: 2px;
      }
      .sub-types-title + span {
        margin-left: -2px;
        width: 240px;
        // text-align: right;
        input {
          padding-left: 10px;
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }
      }
    }
  }
}
.rename-btn {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.trfoldertree {
  height: calc(~"100vh - 160px");
  overflow: auto;
}
.thumb-view {
  display: flex;
  justify-content: center;
  font-size: 25px;
  cursor: pointer;
  height: 100%;
  align-items: center;
}
.invalid-materials {
  color: #f5222d;
}
</style>
