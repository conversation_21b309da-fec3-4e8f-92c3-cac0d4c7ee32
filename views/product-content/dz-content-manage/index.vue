<template>
  <div class="all-background product-content">
    <div class="template-content">
      <div @click="treeshow = !treeshow" class="hiddentreebtn" :style="{ left: !treeshow ? '0px' : '260px' }">
        <a-icon :type="!treeshow ? 'menu-unfold' : 'menu-fold'"/>
      </div>
      <!-- 默认占位 -->
      <div class="template-title" v-loading='treeLoading' v-show="treeshow"></div>
      <VueDragResize v-show="treeshow" :w="260" :minw="260" contentClass="template-title template-title-border" :sticks="['mr']" :isDraggable="false" style="z-index: 50;">
        <div class="template-title">
          <div class="tree-title">
            <p class="title">{{ $t("分类结构") }}
              <jw-icon title="刷新" style="cursor: pointer;" type="jwi-iconrefresh" @click.native="onRefreshFolder"/></p></div>
          <a-tree ref="folderTree" class="trfoldertree" :replaceFields="replaceFields" :selectedKeys='selectedKeys' :expanded-keys="expandedKeys" :auto-expand-parent="autoExpandParent" :tree-data="contentTreeData" @expand="onExpand" @select="onSelect">
            <template #title="{ title, key, dataRef ,count}">
              <div class="tree-self">
                <jw-icon :type="
                  dataRef.childType === 'child'
                    ? (dataRef.modelIcon|| '#jwi-wenjianga-youneiyong')
                    : (dataRef.modelIcon||'#jwi-chanpin dataRef.modelIcon')
                "/>
                <span class="tree-self-title" :title="title">
                {{ title }}
              </span>
                <div class="tree-self-opration-folder">
                <span @click="openEditTeam(dataRef)">
                  <jw-icon type="jwi-iconedit"></jw-icon>
                </span>
                  &nbsp;
                </div>
                <span>{{ dataRef.count }}</span>
              </div>
            </template>
          </a-tree>
        </div>
      </VueDragResize>
      <div class="template-list">
        <a-spin :spinning="cardSpinning">
          <jwToolbar :toolbars='toolbars' @click="onToolClick">

            <template slot="before-end">
              <a-button :loading="syncLoading" v-if="isAdmin" @click="onToolClick({name: '',position: 'after',key: 'syncElePart'})">
                同步
              </a-button>

              <a-select style="width: 100px" v-model.trim='searchParams.modelDefinition' :placeholder="$t('类型')"
                        @change='onSearch' allowClear>
                <a-select-option v-for="(item,i) in typeOptions" :key="i" :value="item.val">
                  {{ item.txt }}
                </a-select-option>
              </a-select>
              <a-select style="width: 100px" v-model.trim="searchParams.supplierOid" :placeholder="$t('供应商')"
                        @change='onSearch' allowClear>
                <a-select-option v-for="(item,i) in supplierOptions" :key="i" :value="item.val">
                  {{ item.txt }}
                </a-select-option>
              </a-select>
              <a-select style="width: 100px" v-model.trim='searchParams.preferenceLevel' :placeholder="$t('优选等级')"
                        @change='onSearch' allowClear>
                <a-select-option v-for="(item,i) in levelOptions" :key="i" :value="item.val">
                  {{ item.txt }}
                </a-select-option>

              </a-select>
              <a-input v-model.trim="searchParams.searchKey" @input='delaySearch' style="width: 150px" allowClear
                       placeholder="请输入编码,名称 ">
                <a-icon slot="prefix" type="search"/>
              </a-input>
            </template>

            <template slot="after-start">
              <a-tag class="table-tag" v-if="switchType&&selectedRows.length" color="blue" closable
                     @close="clearTableSelect">已选中{{ selectedRows.length }}项
              </a-tag>
              <a-button style="margin-left:8px;" class="ant-btn-icon-only"
                        @click="onToolClick({name: '',position: 'after',key: 'switch'})">
                <jw-icon :type=" this.switchType === false ? 'jwi-iconlogo-windows-filled' : 'jwi-iconlist'">
                </jw-icon>
              </a-button>
              <batch-operator
                  ref="batch-operator"
                  @reloadpage="reFetchData"
                  :selectList.sync="selectedRows"
                  :containerOid="$route.query.oid"
                  :containerModel="$route.query.modelDefinition"
                  :containerType="$route.query.type"
                  :erp="true"/>
              <a-dropdown placement="bottomRight">
                <a-menu slot="overlay">
                  <a-menu-item
                      key="ecadExcelTemp"
                      @click="openImportModal('EcadTemp')"
                  >{{'导入ECAD'}}</a-menu-item>
                  <a-menu-item
                      key="partDocumentTemp"
                      @click="openImportModal('PartTemp')"
                  >{{ $t("部件模板导入") }}
                  </a-menu-item
                  >
                </a-menu>
                <a-button class="ant-btn-icon-only">
                  <jw-icon type="jwi-iconImport"/>
                </a-button>
              </a-dropdown>
              <a-dropdown :disabled=!isAdmin placement="bottomRight">
                <a-menu slot="overlay">
                  <a-menu-item
                      key="partDocumentTemp"
                      @click="exportPart"
                  >{{ $t("btn_export") + $t("txt_part") }}</a-menu-item
                  >
                  <a-menu-item
                      key="documentTemp"
                      @click="exportDocument"
                  >{{ $t("导出文档") }}</a-menu-item
                  >
                </a-menu>
                <a-button :loading="exportLoading">
                  <jw-icon type="jwi-iconexport" />
                </a-button>
              </a-dropdown>
            </template>

          </jwToolbar>
          <div v-if="switchType" class="table-box">
            <jw-table ref="ref_table" disableCheck="disableCheck" :data-source.sync="tableData" :columns="getHeader"
                      :selectedRows.sync="selectedRows" :pagerConfig="pagerConfig" :checkbox-config="{reserve: true}"
                      @onPageChange="onPageChange"@onSizeChange="onSizeChange" @checkbox-change="onSelectChange" @onOperateClick="onOperateClick" @on-checkbox-change="onCheckboxChange" @checkbox-all="onCheckboxChange">

              <template #thumbSlot="{ row }">
                <!-- 2D图类型 -->
                <div class="thumb-view" :title="$t('txt_filePreview')" @click="onPreview(row)"
                     v-if="valid2d(row.modelDefinition)">
                  <jwIcon type="#jwi-PDFwendang"/>
                </div>
                <!-- 3D图类型 -->
                <cad-thumbnail v-else-if="valid3d(row.modelDefinition)" :currentRow="row"></cad-thumbnail>
              </template>
              <template #numberSlot="{ row }">
                <router-link target='_blank' :to="{
                  path: '/detailPage',
                  query: {oid: row.oid, type: row.type, masterType: row.masterType, modelDefinition: row.modelDefinition, tabActive: 'product'}
                }">
                  <jwIcon :type="row.modelIcon"/>
                  {{ row.number }}
                </router-link>
              </template>
              <template #nameSlot="{ row }">
                {{ row.cname || row.name }}
              </template>
              <template #updateDate="{ row }">
                <span>
                  {{ formatDateFn(row.updateDate) }}
                </span>
              </template>
              <template #isLock="{ row }">
                <a-tooltip>
                  <template slot="title">
                    {{ row.lockOwnerAccount }} {{ $t("txt_check_out") }}
                  </template>
                  <jw-icon v-if="row.lockOwnerOid" :type="
                    row.lockOwnerOid === jwUser.oid
                      ? '#jwi-beiwojianchu'
                      : '#jwi-bierenjianchu'
                  "/>
                </a-tooltip>
              </template>
              <template #loadStatus="{ row }">
                <jw-icon v-if="!row.loadStatus" type="#jwi-liuchengzhong"/>
              </template>
              <template #versionSlot="{ row }">
                <span>
                  {{ row.displayVersion }}
                </span>
              </template>
              <template #owner="{ row }">
                <user-info :accounts="[row.owner]" :showname="true"/>
              </template>
              <template #operation="{ row }">
                <operation-dropdown :current-record="row" @complete="reFetchData" @fetchTable="fetchTable"
                                    :location="selectLocation"
                                    :addIterationAble="addIterationAble"
                                    :currentTree="currentTree" :containerOid="$route.query.oid"
                                    :containerModel="$route.query.modelDefinition" :containerType="$route.query.type"
                                    @batchOperator="batchOperator">
                </operation-dropdown>
              </template>
            </jw-table>
          </div>
          <div v-else class="card-box">
            <card-list :currentTree="currentTree" :tableData="tableData" :pagination="pagerConfig"
                       @reloadData="reFetchData" @batchOperator='batchOperator' @onPageChange="onPageChange"
                       @onSizeChange="onSizeChange"/>
          </div>
        </a-spin>
      </div>
    </div>

    <a-modal
        v-model.trim="renameVisible"
        :title="$t('编辑团队')"
        width="1000px"
        :mask="false"
        @ok="renameVisible = false"
        :wrapClassName="'rename-Modal'"
        destroyOnClose
    >
      <a-row :gutter="16">
        <a-col :span="24">
          <team-manager :folder="currentTree" v-if="renameVisible"></team-manager>
        </a-col>
      </a-row>
      <template slot="footer">
        <a-button
            key="submit"
            type="primary"
            @click="renameVisible = false"
        >
          完成
        </a-button>
      </template>
    </a-modal>

    <create-drawer ref="cerateDrawer" @onRefresh="onRefresh" :territory="territory"></create-drawer>

    <!-- 批量导入元器件 -->
    <batch-import-modal :visible="batchImportePartVisible" :currentTree="currentTree" :epart="true"
                        @close="batchImportePartVisible = false"
                        :contentClsData="this.contentTreeData?this.contentTreeData[0]:[]" @getList="reFetchData">
    </batch-import-modal>
    <!-- 多个文件导入 -->
    <batchImportModelFile
        :objInfo="modelObjInfo"
        :visible="importModelFileVisible"
        :currentTree="currentTree"
        @close="importModelFileVisible = false"
        @getList="reFetchData"
    >
    </batchImportModelFile>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import {jwSimpleTabs, jwAvatar, jwIcon, jwModalForm} from "jw_frame";
import {formatDate} from "jw_utils/moment-date";
import createDrawer from "./create-drawer.vue";
import rules from "../../../utils/rules";
import cardList from "../content-manage/card-list.vue";
import batchImportModal from "../content-manage/batch-import-modal.vue";
import operationDropdown from "components/operation-dropdown";
import {getChainParent} from "utils/util";
import {getCookie} from "jw_utils/cookie";
import cadThumbnail from "components/cad-thumbnail.vue";
import userInfo from "components/user-info";
import commonStore from "jw_stores/common";
import BatchOperator from "../../batch-operator-dialog/batch-operator.vue";
import {jwToolbar} from "jw_frame";
import {findDetail} from "apis/baseapi";
import teamManager from "/views/team-manager";
import {getExcelList} from "../content-manage/apis/index"
import {
  getDropdownList
} from 'apis/part';
import batchImportModelFile from "../content-manage/batch-import-model-file.vue"
import VueDragResize from 'vue-drag-resize'
// 文件夹目录
const fetchfolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/searchFoldersAndCount`,
  method: "post"
});

// 查询目录下的内容

const fetchSubContentApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/fuzzyElectronSubPage`,
  method: "post"
});

// 查询部件，文档，CAD权限
const getOperationFilter = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post"
});


const previewApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.fileMicroServer}/file/getUrlByOidForPreview`,
  method: "get"
});

const supplierApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/supplier/fuzzyPage`,
  method: "post"
});

const fetchSubModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/query-sub-model`,
  method: "post"
});

const refreshClsFolderModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/refreshClsFolder`,
  method: "post"
});

//同步电子件
const syncElePartToAccess = ModelFactory.create({
  // url: `${Jw.gateway}/${Jw.cadService}/eda/ad/syncLibrary`,
  url: `${Jw.gateway}/${Jw.customerServer}/eda/ad/syncLibrary`,

  // method: 'get'
  method: 'post'
})

const initUserPermission = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/v1/permission/policy/initPermission`,
  method: "get",
})


export default {
  props: ['territory'],
  components: {
    jwModalForm,
    jwSimpleTabs,
    jwAvatar,
    jwIcon,
    createDrawer,
    cardList,
    operationDropdown,
    cadThumbnail,
    BatchOperator,
    batchImportModal,
    jwToolbar,
    teamManager,
    batchImportModelFile,
    userInfo,
    VueDragResize
  },
  data() {
    this.typeOptions = [
      {
        txt: "元器件",
        val: "Part"
      },
      {
        txt: "图符",
        val: "Symbol"
      },
      {
        txt: "封装",
        val: "Encapsulation"
      },
      {
        txt: "datasheet",
        val: "Datasheet"
      }
    ];
    this.levelOptions = [
      {
        txt: "优选",
        val: "优选"
      },
      {
        txt: "非优选",
        val: "非优选"
      },
      {
        txt: "禁选",
        val: "禁选"
      }
    ];
    return {
      isAdmin:false,
      syncLoading: false,
      treeLoading: true,
      batchImportePartVisible: false,
      selectedKeys: [],
      searchParams: {
        supplierOid: undefined,
        preferenceLevel: undefined,
        modelDefinition: "Part",
        searchKey: undefined
      },
      supplierOptions: [],
      batchMcadVisible: false,
      //是否显示树
      treeshow: true,
      // 导入导出
      exportLoading: false,
      // 文件夹重命名
      renameForm: {
        renameValue: ""
      },
      renameLoading: false,
      renameVisible: false,
      renameRules: {
        renameValue: [
          {required: true, message: this.$t("msg_input"), trigger: "change"}
        ]
      },
      // 全部数据配置
      subTypes: [
        "PartIteration",
        // "DocumentIteration",
        // "MCADIteration",
        "ECADIteration"
      ],
      subTypesTitle: this.$t("txt_all_datas"),
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0,
        pageSizeOptions: ["10", "20", "50", "100","200", "500", "1000"]
      }, //分页配置
      cardSpinning: true,
      switchType: true, //  切换卡片和表格形式
      // tab选项
      tabValue: "product",
      tabOption: [
        {value: "product", label: this.$t("txt_content_management")},
        {value: "resource", label: this.$t("txt_product_structure")},
        {value: "provider", label: this.$t("txt_change_management")},
        {value: "provider", label: this.$t("txt_baseline_management")}
      ],
      jwUser: Jw.getUser(),
      // 新增团队表单信息
      currentTree: {},
      treeNameAll: "",
      rules,
      createLoading: false,
      createRules: {
        name: [
          {required: true, message: this.$t("txt_input"), trigger: "change"}
        ]
      },
      addFoldVisible: false,
      addFoldParams: {
        name: "",
        description: ""
      },
      // 带搜索树形结构
      replaceFields: {
        key: "oid",
        title: "name",
        children: "children"
      },
      expandedKeys: [],
      subModelOptions: [],
      searchValue: "",
      autoExpandParent: true,
      // 标题数据源
      contentTreeData: [],
      //是否可以进行筛选 原型创建标识
      addIterationAble:false,
      //当前选择树结构信息
      selectLocation:{},
      // 没有子的节点
      noChildNode:[],
      tableData: [{}],
      selectedRows: [],
      confirmLoadingStatus: false,
      pagination: {
        page: 1,
        size: 10,
        total: 30
      },
      tableLoading: false,
      total: 0,
      selectRow: [],

      // 部件操作权限
      permissionList: [],
      // 文件夹操作权限
      folderRole: false,

      batchImportVisible: false,

      visiblePartBomExport: false,

      //模板导入
      modelObjInfo: {},
      importModelFileVisible: false
    };
  },
  computed: {
    getHeader() {
      return [
        {
          field: "isLock",
          title: "",
          params: {
            showHeaderMore: false
          },
          align: "center",
          width: 38,
          slots: {
            default: "isLock"
          }
        },
        {
          field: "thumbnailOid",
          title: "",
          params: {
            showHeaderMore: false
          },
          width: "40px",
          className: "thumbSlotclass",
          slots: {
            default: "thumbSlot"
          }
        },
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true,
          width: "250px",
          slots: {
            default: "numberSlot"
          }
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          width: "250px",
          sortable: true,
          slots: {
            default: "nameSlot"
          }
        },
        {
          field: "modelDefinition",
          title: this.$t("txt_type"),
          sortable: true
        },
        {
          field: "extensionContent.cn_jwis_gg",
          width: "250px",
          title: this.$t("规格"),
        },
        /*{
          field: "preferenceLevel",
          title: this.$t("优选等级"),
          sortable: true
        },*/
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          sortable: true
        },
        {
          field: "displayVersion",
          title: this.$t("txt_plan_version"),
          sortable: true,
          slots: {
            default: "versionSlot"
          }
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_date"),
          sortable: true, // 开启排序
          slots: {
            default: "updateDate"
          }
        },
        {
          field: "owner",
          title: this.$t("txt_owner"),
          sortable: true, // 开启排序
          slots: {
            default: 'owner',
          }
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          slots: {
            default: "operation"
          }
        }
      ];
    },
    toolbars() {
      let customToolBar = [
        {
          name: this.$t("txt_part"),
          position: "before",
          type: "default",
          key: "Part",
          prefixIcon: "#jwi-lianjian",
          isVisible: this.permissionList.includes("Part.create")
        },
        {
          name: "ECAD",
          position: "before",
          type: "default",
          key: "ECAD",
          prefixIcon: "#jwi-wendang",
          isVisible:
              this.$route.query.modelDefinition === "ResourceContainer" &&
              this.permissionList.includes("ECAD.create")
        }
      ];
      return customToolBar;
    }
  },
  watch: {
    switchType() {
      this.selectedRows = []
    },
    tableData() {
      //this.selectedRows = []
      const t0 = this.$refs.ref_table.getCheckboxReserveRecords(),
          t1 = this.$refs.ref_table.getCheckboxRecords();

      this.selectedRows = [...t0, ...t1];
    }
  },
  created() {
    // 输入回调去抖动
    this.delaySearch = _.debounce(this.onSearch, 500);
    this.fetchFold();
    // this.operationFilter();

    this.initsupplierList();
  },

  methods: {
    onCheckboxChange(){
      const t0 = this.$refs.ref_table.getCheckboxReserveRecords(),
          t1 = this.$refs.ref_table.getCheckboxRecords();

      this.selectedRows = [...t0, ...t1];
    },
    checkAdmin() {
      this.isAdmin = Jw.getUser().systemAdmin || Jw.getUser().dataAdmin || Jw.getUser().tenantAdmin;
      console.log("admin : " ,this.isAdmin);
    },
    initUserPermission(){
      initUserPermission
          .execute()
          .then(data => {})
          .catch(err => {
            if (err.msg) {
              this.$error(err.msg);
            }
          });
      this.checkAdmin();
    },
    openImportModal(type) {
      this.modelObjInfo = getExcelList(type)
      this.importModelFileVisible = true
    },
    async loadingList(val) {
      if (!val) {
        return
      }
      this.loading = true
      let params = {
        viewCode: 'FOLDERINSTANCEOPERATION',
        objectOid: val.oid,
      };
      const resp = await getDropdownList.execute(params)
      const editFun = resp.find(item => item.code === 'rename')
      return editFun && editFun.status === 'enable'
    },
    openEditTeam(data) {
      this.loadingList(data).then(resp => {
        if (resp) {
          this.currentTree = data;
          this.renameVisible = true
        } else {
          this.$error('无团队编辑权限')
        }
      })

    },
    onRefreshFolder() {
      let rootFolder = this.contentTreeData[0];
      let {oid, type} = rootFolder;

      findDetail.execute({oid, type}).then(res => {
        let params = {clsOid: res.clsOid, rootOid: rootFolder.oid};
        refreshClsFolderModel.execute(params).then(res => {
          this.fetchFold();
        });
      });
    },
    initsupplierList() {
      supplierApi.execute().then(res => {
        let {rows} = res;
        this.supplierOptions = rows.map(item => {
          return {
            txt: item.manufacturerName,
            val: item.manufacturerName
          };
        });
      });
    },

    //2d文档显示
    valid2d(modelDefinition) {
      return (
          modelDefinition == "CADDrawing" ||
          modelDefinition == "PCB" ||
          modelDefinition == "Schematic" ||
          modelDefinition == "CADLayout" ||
          modelDefinition == "CAD2DSketch" ||
          modelDefinition == "CADCEDrawing"
      );
    },
    //3d图显示
    valid3d(modelDefinition) {
      return modelDefinition != "Document";
    },
    batchOperator(record, type) {
      this.selectedRows = [record];
      this.$nextTick(() => {
        this.$refs["batch-operator"].validSelect(type);
      });
    },
    onPreview(row) {
      console.log(row.thumbnailOid);
      previewApi
          .execute({
            fileOid: row.thumbnailOid
          })
          .then(url => {
            commonStore.set("query", url);
            window.open("#/preview", "_blank");
          })
          .catch(err => {
            if (err.code === -1) {
              this.$error(this.$t("没有可预览的文件"));
            }
          })
          .finally(() => {
          });
    },
    // 显示重命名文件夹模态框
    showRename(dataRef) {
      this.renameDataRef = dataRef;
      this.renameForm.renameValue = dataRef.name;
      this.renameVisible = true;
    },

    gotoDetails(row) {
      Jw.jumpToDetail(row, {tabActive: "info"});
    },

    // 查询部件，文档，CAD权限
    operationFilter(FolderOid) {
      this.permissionList = []
      let {oid} = this.$route.query
      let param = null
      if (FolderOid) {
        param = {
          viewCode: "NOINSTANCE",
          objectType: "Folder",
          objectOid: FolderOid,
        }
      } else {
        param = {
          viewCode: "NOINSTANCE",
          objectType: "Container",
          objectOid: oid,
        }
      }

      getOperationFilter
          .execute(param)
          .then(data => {
            if (data) {
              data.forEach(item => {
                if (item.status == "enable") {
                  this.permissionList.push(
                      item.modelType + "." + item.permissionKey
                  );
                }
              });
            }
          })
          .catch(err => {
            console.error(err);
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },

    // 扁平化树形结构数组
    flatten(array) {
      var flattend = [];
      (function flat(array) {
        array.forEach(function (el) {
          for (let i in el) {
            if (Object.prototype.toString.call(el[i]) === "[object Array]") {
              flat(el[i]);
            }
          }
          flattend.push(el);
        });
      })(array);
      return flattend;
    },
    deepData(data, scopedSlots) {
      let _this = this;
      data.map((item, index) => {
        item.title = item.name;
        item.key = item.oid;
        item.value = item.oid;
        if (scopedSlots) {
          item.scopedSlots = scopedSlots;
          if (scopedSlots.title) delete item.title;
        }
        if (item.children && item.children.length > 0) {
          item.children.map(item => (item.childType = "child"));
          _this.deepData(item.children, scopedSlots);
        } else {
          this.noChildNode.push(item.oid)
        }
      });
      return data;
    },
    // 获取文件夹树形目录结构
    fetchFold() {
      this.initUserPermission();
      let {query} = this.$route;
      let param = {
        containerOid: query.oid,
        containerModel:
            query.masterType || query.modelDefinition || query.containerModel,
        subTypes: this.subTypes
      };
      this.treeLoading = true;
      fetchfolderTree
          .execute(param)
          .then(data => {
            this.treeLoading = false;
            /**
             * @param treeName
             *
             * 从地址栏获取treeName，
             * 如果存在/，需要展开并且选中/后面的文件夹
             * 如果只存在顶级节点，则加载顶级节点文件夹
             *
             * */
            this.noChildNode = []
            this.contentTreeData = this.deepData(data)
            let defaultExpandedKeys = data[0].oid
            localStorage.setItem("contenttreelocation", defaultExpandedKeys)
            let flattenData = this.flatten(data)
            let routeTreeName = this.filterRouteName()
            if (routeTreeName) {
              let routeTree = flattenData.filter(
                  (item) => item.name === routeTreeName
              )[0]
              this.selectedKeys = [routeTree.oid]
              this.expandedKeys = [routeTree.oid]
              this.fetchTable(routeTree)
              if (data.length > 0) {
                this.currentTree = routeTree
              }
              this.operationFilter(routeTree.oid)
            } else {
              this.fetchTable(this.deepData(data)[0])
              this.expandedKeys = [defaultExpandedKeys]
              if (data.length > 0) {
                this.currentTree = data[0]
                this.selectedKeys = [this.currentTree.oid]
                this.operationFilter(this.currentTree.oid)
              }
            }
          })
          .catch(err => {
            this.treeLoading = false;
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    filterRouteName() {
      if (
          this.$route.query.treeName &&
          this.$route.query.treeName.lastIndexOf("/") > 0
      ) {
        /**
         *
         * 判断是否有下划线/
         * 如果有下划线 匹配当前树的数据
         * 触发树选择事件
         *
         * */
        let treeName = this.$route.query.treeName;
        let currentNameIndex = treeName.lastIndexOf("/") + 1;
        let currentName = treeName.substring(currentNameIndex, treeName.length);
        return currentName;
      }
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    onSelect(value, node, extra) {
      let {dataRef} = node.node;
      this.currentTree = dataRef;
      //储存当前结构树上选择目录
      this.selectLocation={catalogOid:this.currentTree.oid,catalogType:this.currentTree.type}
      this.selectedKeys = [this.currentTree.oid];
      localStorage.setItem("contenttreelocation", this.currentTree.oid);
      this.pagerConfig = {
        current: 1,
        pageSize: 20,
        total: 0
      };
      this.operationFilter(dataRef.oid)
      this.fetchTable(dataRef);
    },
    getCurrentTree() {
      return this.currentTree;
    },
    tabChange(option) {
      let {value} = option;
      this.tabValue = value;
      this.fetchTable({current: 1, pageSize: 20});
    },
    // 选择列回调
    onSelectChange(args) {
      this.selectRow = args;
    },
    // 操作列回调
    onOperateClick(key, row) {
      if (key === "lanuch") {
        ///
      } else if (key === "delete") {
        this.onDelete(row);
      }
    },
    // 工具栏点击回调
    onToolClick(item) {
      let locationInfo = {
        disabled: true,
        catalogOid: this.currentTree.oid,
        catalogType: this.currentTree.type,
        catalogName: this.currentTree.name,
        containerOid: this.$route.query.oid,
        containerType: this.$route.query.type,
        containerModelDefinition: this.$route.query.modelDefinition
      }
      if (item.key === "create") {
      } else if (item.key === "delete") {
        this.fetchDelete(this.selectedRows);
      } else if (item.key === "Part") {
        if(!this.noChildNode.includes(this.currentTree.oid)) {
          this.$error("部件必须在最底层创建");
          return
        }
        this.$refs.cerateDrawer.show({
          title: this.$t("btn_new_create") + item.name,
          modelInfo: {
            layoutName: "create",
            modelName: "Part"
          },
          params: {
            url: `${Jw.gateway}/${Jw.customerServer}/part/create`,
            locationInfo,
            activeModle: "ElectricalPart"
          }
        });
      } else if (item.key === "ECAD") {
        this.$refs.cerateDrawer.show({
          title: this.$t("btn_new_create") + item.name,
          modelInfo: {
            layoutName: "create",
            modelName: "ECAD"
          },
          params: {
            url: `${Jw.gateway}/${Jw.cadService}/ecad/create`,
            locationInfo
          }
        });
      } else if (item.key === "switch") {
        // 切换列表
        this.switchType = !this.switchType;
        this.reFetchData();
      } else if (
          item.key === "all" ||
          item.key === "PartIteration" ||
          item.key === "DocumentIteration" ||
          item.key === "MCADIteration" ||
          item.key === "ECADIteration"
      ) {
        this.subTypesTitle = item.name;
        if (item.key === "all") {
          this.subTypes = [
            "PartIteration",
            "DocumentIteration",
            "MCADIteration",
            "ECADIteration"
          ];
        } else {
          this.subTypes = [item.key];
        }
        this.pagerConfig = {
          current: 1,
          pageSize: 20,
          total: 0
        };
        this.reFetchData();
      } else if (item.key === 'syncElePart') {
        //同步电子件到access库共享文件
        this.snycElePartDbFile()
      }
    },

    //同步电子件文件
    snycElePartDbFile() {
      this.$confirm({
        title: this.$t('txt_tips_top'),
        content: this.$t('confirm_sync_ele_part'),
        okText: this.$t('btn_ok'),
        type: 'warning',
        cancelText: this.$t('btn_cancel'),
        onOk: () => {
          let param = this.selectedRows.map(item => item.oid);
          this.syncLoading = true
          syncElePartToAccess.execute(
              {
                containerOid: this.$route.query.oid,
                selectList:param
          }).then(() => {
            this.$success(this.$t('sync_success'))
          }).catch(e => {
            this.$error(e.msg || this.$t('txt_user_sync_error'))
          }).finally(() => {
            this.syncLoading = false
          })
        }
      })
    },


    onCloseDownModal() {
      this.visibleDown = false;
    },

    // 导出part数据
    exportPart() {
      let {selectedRows, currentTree, $route, subTypes} = this;
      let oidList = selectedRows.map(item => item.oid);
      const accesstoken = getCookie("token");
      this.exportLoading = true;
      fetch(
          `${Jw.gateway}/${Jw.customerServer}/part-bom-micro/part-export/export-part-info`,
          {
            method: "post",
            body: JSON.stringify({
              tenantOid: getCookie("tenantOid"),
              fromOid: currentTree.oid,
              fromType: currentTree.type,
              containerName: $route.query.containerName,
              oidList: oidList
              // subTypes,
            }),
            headers: {
              "Content-Type": "application/json;charset=utf8",
              appName: Jw.appName,
              accesstoken,
              tenantAlias: getCookie("tenantAlias"),
              tenantOid: getCookie("tenantOid")
            }
          }
      )
          .then(response => {
            return response.blob();
          })
          .then(data => {
            this.$success(this.$t("txt_export_success"));
            this.downBlob(data, currentTree.name, 'xlsx')
            this.exportLoading = false;
          })
          .catch(err => {
            console.error(err);
            this.exportLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    //导出文档
    exportDocument() {
      let oidList = this.selectedRows.map(item => item.oid);
      let subTypes = [
        "DocumentIteration",
        "MCADIteration",
        "ECADIteration"
      ];
      let {current, pageSize} = this.pagerConfig;
      let param = {
        subTypes,
        fromOid: this.currentTree.oid, // 目录/容器oid
        fromType: "Folder", // 目录/容器类型
        index: current, // 分页页数
        size: pageSize,
        searchKey: this.searchParams.searchKey || "",
        oidList:oidList
      };
      const accesstoken = getCookie("token");
      this.exportLoading = true;
      fetch(
          `${Jw.gateway}/customer/customerContainer/exportDocument`,
          {
            method: "post",
            body: JSON.stringify(param),
            headers: {
              "Content-Type": "application/json;charset=utf8",
              appName: Jw.appName,
              accesstoken,
              tenantAlias: getCookie("tenantAlias"),
              tenantOid: getCookie("tenantOid")
            }
          }
      )
          .then(response => {
            return response.blob();
          })
          .then(data => {
            this.$success(this.$t("txt_export_success"));
            this.downBlob(data, this.currentTree.name)
            this.exportLoading = false;
          })
          .catch(err => {
            console.error(err);
            this.exportLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    downBlob(data, name, fileType = 'zip') {
      let url = window.URL.createObjectURL(
          new Blob([data], {
            type: "application/vnd.ms-excel",
          })
      )
      let link = document.createElement("a")
      link.href = url
      link.setAttribute("download", `${name}.${fileType}`)
      document.body.appendChild(link)
      link.click()
    },
    // 删除
    onDelete(row) {
      this.fetchDelete([row]);
    },
    fetchSubModel() {
      fetchSubModel
          .execute({
            viewCode: "ENTITY_FILTER",
            objectType: "Part",
            contextType: this.$route.query.type,
            contextOid: this.$route.query.oid
          })
          .then(res => {
            this.subModelOptions = res;
          })
          .catch(err => {
            this.$error(err.msg);
          });
    },
    // 数据请求函数
    fetchTable(dataRef) {
      try {
        this.treeNameAll = getChainParent(
            this.contentTreeData,
            dataRef.oid,
            "oid",
            "/",
            true
        ).nameStr;
      } catch (error) {
        console.error(error.message);
      }
      let {subTypes} = this;
      let {current, pageSize} = this.pagerConfig;
      let searchParams = {...this.searchParams};
      if (searchParams.modelDefinition === "Part") {
        searchParams.modelDefinition = undefined;
        subTypes = ["PartIteration"];
      }
      let param = {
        subTypes,

        fromOid: dataRef.oid, // 目录/容器oid
        fromType: "Folder", // 目录/容器类型
        index: current, // 分页页数
        size: pageSize,
        ...searchParams
      };
      this.tableLoading = true;
      this.cardSpinning = true;
      return fetchSubContentApi
          .execute(param)
          .then(data => {
            //判断当前选择目录是否为最下层
            this.addIterationAble = this.noChildNode.includes(this.currentTree.oid)
            this.tableLoading = false;
            this.cardSpinning = false;
            this.tableData = data.rows;
            this.pagerConfig.total = data.count;
            this.total = data.count;
            return {data: data.rows, total: data.count};
          })
          .catch(err => {
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },

    // 删除列操作
    fetchDelete(row) {
      let param = row.map(item => item.oid);
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
            <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
              {this.$t("btn_batch_delete")}
            </p>
        ),
        content: (
            <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">
              {this.$t("msg_system_del")}
            </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_confirm"),
        onOk: () => {
          deleteContainer
              .execute(param)
              .then(data => {
                this.$success(this.$t("txt_delete_success"));
                //刷新列表
                this.fetchTable({current: 1, pageSize: 20});
              })
              .catch(err => {
                this.$error(err.msg || this.$t("msg_failed"));
              });
        }
      });
    },
    // 输入回调刷新表格数据
    onSearch() {
      this.tableLoading = true;
      //如果是Part，要查出所有子模型

      this.reFetchData();
    },
    // 时间格式化转换
    formatDateFn(date) {
      return formatDate(date);
    },
    //分页操作
    onPageChange(page, pageSize) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.reFetchData();
    },
    onSizeChange(pageSize, page) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;

      this.reFetchData();
    },
    onRefresh() {
      this.fetchFold();
      this.fetchTable({
        oid: this.currentTree.oid,
        type: this.currentTree.type
      });
    },
    reFetchData(key) {
      if (key) {
        this.fetchFold();
      }
      this.fetchTable({
        oid: this.currentTree.oid,
        type: this.currentTree.type
      });
      // 清除表格选中状态
      this.clearTableSelect()
    },
    clearTableSelect() {
      const table = this.$refs.ref_table
      table && table.clearCheckboxReserve()
      table && table.clearCheckboxRow()
      this.selectedRows = []
    },
  }
};
</script>
<style lang="less" scoped>
/deep/ .jw-toolbar-right .ant-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

/deep/ .jw-tool-content.ant-btn
.jw-tool-content-icon.prefix-icon.jwi-iconlogo-windows-filled {
  margin-right: 0;
}

/deep/ .jw-tool-content.ant-btn .jw-tool-content-icon.prefix-icon.jwi-iconlist {
  margin-right: 0;
}

/deep/ .rename-Modal {
  /deep/ .ant-modal {
    left: -15%;
  }
}

/deep/ .jw-page .ant-layout-content {
  overflow: hidden;
}

// tab页签样式
.wrap-class {
  /deep/ .item-class {
    margin: 0 20px 0 0;
    padding: 4px 0;
    color: rgba(30, 32, 42, 0.65);
  }

  /deep/ .item-class-active {
    font-size: 14px;
    font-weight: 500;
    color: rgba(30, 32, 42, 0.85);
  }
}

.product-content {
  height: calc(~"100% - 45px");
  padding-top: 0;
  padding-bottom: 0;
  box-shadow: none;
}

// 处理树形结构宽度不够
/deep/ .ant-tree li .ant-tree-node-content-wrapper {
  width: 90% !important;
}

// 左侧菜单和右侧表格处理
.template-content {
  height: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;

  .hiddentreebtn {
    position: absolute;
    left: 260px;
    top: 8px;
    cursor: pointer;
    z-index: 55;
  }
  .template-title-border{
    border-right: 1px solid rgba(30, 32, 42, 0.15);
    background-color: #fff;
  }
  .template-title {
    height: 100%;
    padding-right: 12px;
    min-width: 280px;
    .title-search {
      margin-bottom: 8px;
      flex: 1;
      cursor: pointer;
    }

    .tree-title {
      margin: 10px 0;
      //   padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: nowrap;

      .title {
        flex: 3;
        font-size: 14px;
        color: rgba(30, 32, 42, 0.85);
      }
    }

    .title-item {
      margin-bottom: 2px;
      padding-left: 12px;
      height: 42px;
      line-height: 42px;
      font-size: 14px;
      color: rgba(30, 32, 42, 0.85);
      cursor: pointer;

      &:hover {
        background: rgba(30, 32, 42, 0.06);
        border-radius: 4px;
      }
    }
  }

  /deep/ .vdr-stick-mr{
    top: 0;
    height: 100% !important;
    margin-top: 0 !important;
    border-radius: 3px;
    background: #eee;
    border: 0px;
    right: -8px !important;
  }

  /deep/ .active:before{
    outline: 0;
  }

  /deep/ .active{
    border: 0px;
    border-right: 1px solid rgba(30, 32, 42, 0.15);
  }


  .template-list {
    height: 100%;
    width: 20px;
    padding: 10px 0 0 10px;
    flex: 1;

    .table-box {
      margin-top: 10px;
      height: calc(~"100% - 42px");
    }

    .card-box {
      margin-top: 10px;
      height: calc(~"100% - 95px");
    }

    .list-item {
      position: relative;
      margin-left: 16px;
      margin-bottom: 16px;
      float: left;
      width: 240px;
      height: 113px;
      padding: 20px;
      background: rgba(30, 32, 42, 0.04);
      border-radius: 4px 4px 0 4px 4px;
      border-radius: 4px 4px 0 4px 4px;
      cursor: pointer;

      &:hover {
        .item-hover {
          display: flex;
        }
      }

      .item-title {
        margin-bottom: 16px;
        font-size: 14px;
        color: rgba(30, 32, 42, 0.85);
      }

      .item-text {
        font-size: 12px;
        color: rgba(30, 32, 42, 0.45);
        text-align: justify;
      }

      .item-hover {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: none;
        align-items: center;
        justify-content: center;
        background: rgba(30, 32, 42, 0.45);
        border-radius: 4px 4px 0 4px 4px;

        .hover-primary {
          margin-right: 12px;
        }
      }
    }
  }
}
</style>

<style lang="less">
/deep/ .ant-tree-node-content-wrapper {
  width: 90%;
}

.tree-self {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-width: 200px;

  .tree-self-title {
    margin-left: 4px;
    width: 65%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
  }

  .tree-self-opration {
    margin-left: 27px;
    display: none;
  }

  &:hover {
    .tree-self-opration {
      display: initial;
    }
  }
}

.template-list {
  .ant-spin-nested-loading,
  .ant-spin-container {
    height: 100%;

    .jw-toolbar-panel > div:first-child {
      .sub-types-title {
        justify-content: space-between;
        // width: 119px;
        // position: absolute;
        // left: 279px;
        z-index: 5;
        // border: 0;
        // padding: 0 8px;
        // margin-left: 12px;
        // height: 29px;
        // border-right: 1px solid #d9d9d9;
        border-right: 0;
        // border-radius: initial;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      button + .sub-types-title {
        margin-right: 2px;
      }

      .sub-types-title + span {
        margin-left: -2px;
        width: 240px;
        // text-align: right;
        input {
          padding-left: 10px;
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }
      }
    }
  }
}

.rename-btn {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.trfoldertree {
  height: calc(~"100vh - 165px");
  overflow: auto;
}

.thumb-view {
  display: flex;
  justify-content: center;
  font-size: 25px;
  cursor: pointer;
  height: 100%;
  align-items: center;
}

.tree-self-opration-folder {
  margin-left: 27px;
  visibility: hidden;
}

.tree-self:hover {
  .tree-self-opration-folder {
    visibility: visible;
  }
}
</style>
